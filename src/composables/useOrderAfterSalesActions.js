import { ref, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { get } from 'lodash-es'
import dayjs from 'dayjs'
import { useAlert } from '@/composables/index.js'
import { useAfterSalesStore } from '@store/modules/afterSales.js'
import { afterSalesProduct } from '@/utils/storage.js'
import {
  applyOrderAfterSalesJD
} from '@/api/interface/order.js'
import {
  applyOrderCancel,
  jdPrompt
} from '@/api/interface/afterSales.js'

/**
 * 订单操作相关的 composable
 * 用于处理订单详情和售后列表页面的共同逻辑
 */
export function useOrderAfterSalesActions() {
  const router = useRouter()
  const $alert = useAlert()
  const afterSalesStore = useAfterSalesStore()
  const expirationPopupVisible = ref(false)

  // 订单状态常量
  const ORDER_STATE = markRaw({
    PENDING_PAYMENT: '0',
    PENDING_DELIVERY_1: '1',
    CANCELLED: '2',
    PENDING_DELIVERY_3: '3',
    PARTIAL_SHIPPED: '4',
    SHIPPING: '5',
    PARTIAL_CANCELLED: '6',
    REJECTED: '7',
    REVOKED: '8',
    COMPLETED: '9',
    REFUNDED: '10',
    PARTIAL_REFUNDED: '11',
    PARTIAL_REFUNDING: '12',
    DELETED: '-1'
  })

  /**
   * 判断是否过期 - 15天售后期限
   * @param {Object} item - 订单项
   * @param {Object} options - 配置选项
   * @param {boolean} options.useSubOrderData - 是否使用子订单数据结构
   * @returns {boolean}
   */
  const isExpires = (item, options = {}) => {
    const { useSubOrderData = false } = options

    let orderDate
    if (useSubOrderData) {
      // OrderDetail 页面的数据结构
      orderDate = get(item, 'subOrderRawData.orderDate') || get(item, 'orderDate')
    } else {
      // OrderAfterSalesList 页面的数据结构
      orderDate = get(item, 'subOrderRawData.orderDate') || get(item, 'expireTime') || get(item, 'orderDate')
    }

    if (!orderDate) return false

    const expDate = dayjs(orderDate).add(15, 'day')
    const now = dayjs()
    return expDate > now
  }

  /**
   * 获取商品项操作按钮
   * @param {Object} item - 订单项
   * @param {Object} options - 配置选项
   * @param {boolean} options.showAddToCart - 是否显示加购物车按钮
   * @param {Function} options.handleAddToCart - 加购物车处理函数
   * @param {boolean} options.useSubOrderData - 是否使用子订单数据结构
   * @param {Object} options.afterSalesInfo - 售后信息
   * @returns {Array}
   */
  const getItemActions = (item, options = {}) => {
    const {
      showAddToCart = false,
      handleAddToCart = null,
      useSubOrderData = false,
      afterSalesInfo = null
    } = options

    const actions = []
    const orderState = item.orderState

    // 获取售后申请列表和相关信息
    let afterSaleApplyList, isApplyAfterSales, supplierCode

    if (useSubOrderData) {
      // OrderDetail 页面的数据结构
      const subOrder = item.subOrderRawData
      afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      isApplyAfterSales = afterSaleApplyList.length > 0
      supplierCode = get(subOrder, 'skuNumInfoList[0].sku.supplierCode', '')
    } else {
      // OrderAfterSalesList 页面的数据结构
      isApplyAfterSales = get(item, 'afterSaleId', '')
      supplierCode = get(item, 'skuNumInfoList[0].sku.supplierCode', '')
    }

    const isExpired = !isExpires(item, { useSubOrderData })
    const isJDSupplier = supplierCode.indexOf('jd_') > -1

    // 加购物车按钮 - 仅在 OrderDetail 页面显示
    if (showAddToCart && handleAddToCart) {
      actions.push({
        key: 'addToCart',
        label: '加购物车',
        type: 'default',
        handler: handleAddToCart
      })
    }

    // 售后按钮逻辑
    const applyType = useSubOrderData ? afterSalesInfo?.applyType : item.applyType

    if (applyType) {
      // 有售后类型配置时的逻辑
      if (applyType === '1') {
        // 申请退款
        if (isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3 || orderState === ORDER_STATE.SHIPPING) && isApplyAfterSales) {
          actions.push({
            key: 'applyRefund',
            label: '申请退款',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => handleAfterSalesAction(item, 0, options) : () => handleAfterSalesAction(item, 0, options)
          })
        } else if (!isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3) && isApplyAfterSales) {
          actions.push({
            key: 'applyRefund',
            label: '申请退款',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => handleAfterSalesAction(item, 0, options) : () => handleAfterSalesAction(item, 0, options)
          })
        }
      } else if (applyType === '2') {
        // 申请售后
        if (isJDSupplier && orderState === ORDER_STATE.COMPLETED && isApplyAfterSales) {
          actions.push({
            key: 'applyAfterSale',
            label: '申请售后',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => handleAfterSalesAction(item, 1, options) : () => handleAfterSalesAction(item, 1, options)
          })
        } else if (!isJDSupplier && (orderState === ORDER_STATE.SHIPPING || orderState === ORDER_STATE.COMPLETED) && isApplyAfterSales) {
          actions.push({
            key: 'applyAfterSale',
            label: '申请售后',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => handleAfterSalesAction(item, 1, options) : () => handleAfterSalesAction(item, 1, options)
          })
        }
      }
    } else {
      // 没有售后类型配置时的默认逻辑
      if (isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3 || orderState === ORDER_STATE.SHIPPING) && !isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => handleAfterSalesAction(item, 0, options)
        })
      } else if (!isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3) && !isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => handleAfterSalesAction(item, 0, options)
        })
      }

      if (isJDSupplier && orderState === ORDER_STATE.COMPLETED && !isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => handleAfterSalesAction(item, 1, options)
        })
      } else if (!isJDSupplier && (orderState === ORDER_STATE.SHIPPING || orderState === ORDER_STATE.COMPLETED) && !isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => handleAfterSalesAction(item, 1, options)
        })
      }
    }

    // 查看详情按钮
    if (isApplyAfterSales) {
      actions.push({
        key: 'viewDetails',
        label: '查看详情',
        type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: () => handleAfterSalesAction(item, 2, options)
      })
    }

    return actions
  }

  /**
   * 申请售后/退款 - 单个商品
   * @param {Object} item - 订单项
   * @param {number} type - 操作类型 0:申请退款 1:申请售后 2:查看详情
   * @param {Object} options - 配置选项
   */
  const handleAfterSalesAction = async (item, type, options = {}) => {
    const { useSubOrderData = false, currentOrderStatus = null } = options

    const isExpired = !isExpires(item, { useSubOrderData })

    let hasAfterSaleId, supplierSubOrderId, orderPrice, orderState, supplierSubOutOrderId, skuNum, sku, supplierCode

    if (useSubOrderData) {
      // OrderDetail 页面的数据结构
      const subOrder = item.subOrderRawData
      const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      hasAfterSaleId = afterSaleApplyList.length > 0

      supplierSubOrderId = get(subOrder, 'id')
      orderPrice = subOrder.orderPrice
      orderState = subOrder.orderState
      supplierSubOutOrderId = subOrder.supplierSubOutOrderId
      const skuInfo = get(subOrder, 'skuNumInfoList[0]', {})
      skuNum = skuInfo.skuNum
      sku = skuInfo.sku
      supplierCode = sku?.supplierCode
    } else {
      // OrderAfterSalesList 页面的数据结构
      hasAfterSaleId = get(item, 'afterSaleId', '')

      supplierSubOrderId = item.supplierSubOrderId
      orderPrice = item.orderPrice
      orderState = item.orderState
      supplierSubOutOrderId = item.supplierSubOutOrderId
      const skuInfo = get(item, 'skuNumInfoList[0]', {})
      skuNum = skuInfo.skuNum
      sku = skuInfo.sku
      supplierCode = sku?.supplierCode
    }

    // 如果已过期且没有售后ID，显示过期提示
    if (isExpired && !hasAfterSaleId) {
      expirationPopupVisible.value = true
      return
    }

    // 设置售后产品信息
    const afterSalesProductInfo = {
      supplierSubOrderId,
      orderState,
      orderPrice,
      skuNum,
      sku,
      supplierCode,
      supplierOutSubOrderId: supplierSubOutOrderId || ''
    }
    afterSalesProduct.set(afterSalesProductInfo)

    // 重置售后信息
    const applyType = useSubOrderData ? options.afterSalesInfo?.applyType : item.applyType
    if (applyType) {
      afterSalesStore.updateAfterSalesInfo({
        applyType: '',
        afterSaleState: '',
        bizOrderId: '',
        bizCode: '',
        orderState: ''
      })
    }

    const isJDSupplier = supplierCode && supplierCode.indexOf('jd_') > -1

    // 获取赠品信息和售后ID
    let giftInfo, afterSaleId, afterSaleApplyType
    if (useSubOrderData) {
      const subOrder = item.subOrderRawData
      giftInfo = get(subOrder, 'giftInfoList', [])
      const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      afterSaleId = afterSaleApplyList[0]?.id
      afterSaleApplyType = afterSaleApplyList[0]?.applyType
    } else {
      giftInfo = +get(item, 'isHaveGift', '0')
      afterSaleId = item.afterSaleId
      afterSaleApplyType = item.applyType
    }

    if (isJDSupplier) {
      await handleJDAfterSales(supplierSubOrderId, supplierCode, type, giftInfo, afterSaleId, afterSaleApplyType, orderState, currentOrderStatus)
    } else {
      await handleNonJDAfterSales(supplierSubOrderId, type, afterSaleId, afterSaleApplyType, orderState)
    }
  }

  /**
   * 处理京东售后
   * @param {string} supplierSubOrderId - 供应商子订单ID
   * @param {string} supplierCode - 供应商代码
   * @param {number} type - 操作类型
   * @param {Array|number} giftInfo - 赠品信息
   * @param {string} afterSaleId - 售后ID
   * @param {string} applyType - 申请类型
   * @param {string} orderState - 订单状态
   * @param {string} currentOrderStatus - 当前订单状态
   */
  const handleJDAfterSales = async (supplierSubOrderId, supplierCode, type, giftInfo, afterSaleId, applyType, orderState, currentOrderStatus) => {
    try {
      const [err, res] = await jdPrompt({ supplierSubOrderId, supplierCode })
      if (err) {
        showToast(err.msg)
        return
      }

      const prompt = res
      const finalOrderState = currentOrderStatus || orderState

      if (finalOrderState === ORDER_STATE.COMPLETED) {
        // 已签收状态
        if (type === 1) { // 申请售后
          // 检查是否有赠品 - 兼容两种数据结构
          const hasGift = Array.isArray(giftInfo) ? giftInfo.length > 0 : giftInfo > 0

          if (hasGift) {
            $alert({
              title: '',
              message: '该商品有赠品，如申请售后，请将赠品一同寄回。',
              confirmButtonText: '确定申请',
              cancelButtonText: '暂不申请',
              showCancelButton: true,
              onConfirmCallback: () => {
                applyJDAfterSales(supplierSubOrderId, 1)
              }
            })
            return
          }
          await applyJDAfterSales(supplierSubOrderId, 1)
        } else if (type === 2) { // 查看详情
          await applyJDAfterSales(supplierSubOrderId, 2)
        }
      } else {
        // 其他状态
        if (type === 0) { // 申请退款
          $alert({
            title: '',
            message: prompt,
            confirmButtonText: '确定申请',
            cancelButtonText: '暂不申请',
            showCancelButton: true,
            onConfirmCallback: () => {
              applyJDRefund(supplierSubOrderId)
            }
          })
        } else if (type === 1) { // 申请售后
          await applyJDAfterSales(supplierSubOrderId, 1)
        } else if (type === 2) { // 查看详情
          if (applyType === '1') {
            router.push({
              path: '/wo-after-sales-detail',
              query: {
                afterSaleId,
                type: +applyType
              }
            })
          } else {
            await applyJDAfterSales(supplierSubOrderId, 2)
          }
        }
      }
    } catch (error) {
      showToast('操作失败，请重试')
      console.error('京东售后处理错误:', error)
    }
  }

  /**
   * 处理非京东售后
   * @param {string} supplierSubOrderId - 供应商子订单ID
   * @param {number} type - 操作类型
   * @param {string} afterSaleId - 售后ID
   * @param {string} applyType - 申请类型
   * @param {string} orderState - 订单状态
   */
  const handleNonJDAfterSales = async (supplierSubOrderId, type, afterSaleId, applyType, orderState) => {
    try {
      if (type === 0) { // 申请退款
        $alert({
          title: '',
          message: '您确定申请退款吗？',
          confirmButtonText: '确定申请',
          cancelButtonText: '暂不申请',
          showCancelButton: true,
          onConfirmCallback: () => {
            applyRefund(supplierSubOrderId)
          }
        })
      } else if (type === 1) { // 申请售后
        router.push({
          path: '/wo-after-sales-entry',
          query: {
            orderState: orderState
          }
        })
      } else if (type === 2) { // 查看详情
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: +applyType
          }
        })
      }
    } catch (error) {
      showToast('操作失败，请重试')
      console.error('非京东售后处理错误:', error)
    }
  }

  /**
   * 京东退款申请
   * @param {string} supplierSubOrderId - 供应商子订单ID
   */
  const applyJDRefund = async (supplierSubOrderId) => {
    try {
      showLoadingToast()
      const [err, res] = await applyOrderCancel({ supplierSubOrderId })
      closeToast()

      if (!err) {
        const afterSaleId = res
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: 1
          }
        })
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请退款失败')
      console.error('京东退款申请错误:', error)
    }
  }

  /**
   * 京东售后申请
   * @param {string} supplierSubOrderId - 供应商子订单ID
   * @param {number} firstUrl - 首次URL标识
   */
  const applyJDAfterSales = async (supplierSubOrderId, firstUrl) => {
    try {
      showLoadingToast()
      const [err, json] = await applyOrderAfterSalesJD({
        supplierSubOrderId,
        firstUrl
      })
      closeToast()

      if (!err) {
        window.location.href = json
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请售后失败')
      console.error('京东售后申请错误:', error)
    }
  }

  /**
   * 非京东退款申请
   * @param {string} supplierSubOrderId - 供应商子订单ID
   */
  const applyRefund = async (supplierSubOrderId) => {
    try {
      showLoadingToast()
      const [err, res] = await applyOrderCancel({ supplierSubOrderId })
      closeToast()

      if (!err) {
        const afterSaleId = res
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: 1
          }
        })
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请退款失败')
      console.error('退款申请错误:', error)
    }
  }

  return {
    ORDER_STATE,
    expirationPopupVisible,
    isExpires,
    getItemActions,
    handleAfterSalesAction,
    handleJDAfterSales,
    handleNonJDAfterSales,
    applyJDRefund,
    applyJDAfterSales,
    applyRefund
  }
}
